package com.hzy.client.service;

import cn.hutool.core.util.DesensitizedUtil;
import com.alibaba.fastjson2.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.github.binarywang.wxpay.bean.order.WxPayAppOrderResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.hzy.client.controller.core.AppBaseController;
import com.hzy.core.config.RedisCache;
import com.hzy.core.constant.RedisKeyConsts;
import com.hzy.core.entity.*;
import com.hzy.core.enums.*;
import com.hzy.core.exception.AppException;
import com.hzy.core.mapper.*;
import com.hzy.core.model.vo.AjaxResult;
import com.hzy.core.model.vo.app.*;
import com.hzy.core.service.AliPayService;
import com.hzy.core.service.JxPayService;
import com.hzy.core.service.SysDictTypeService;
import com.hzy.core.service.pay.PayService;
import com.hzy.core.utils.*;
import com.hzy.core.utils.JsonUtils;
import com.hzy.core.utils.OrderUtils;
import com.hzy.core.utils.StringUtils;
import com.hzy.core.utils.ip.IpUtils;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.RSAPublicKeyConfig;
import com.wechat.pay.java.service.payments.app.AppServiceExtension;
import com.wechat.pay.java.service.payments.app.model.Amount;
import com.wechat.pay.java.service.payments.app.model.PrepayRequest;
import com.wechat.pay.java.service.payments.app.model.PrepayWithRequestPaymentResponse;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.AmountReq;
import com.wechat.pay.java.service.refund.model.CreateRequest;
import com.wechat.pay.java.service.refund.model.Refund;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 统一购买服务
 */
@Slf4j
@Service
public class AppBuyService extends AppBaseController {

    @Resource
    private Redisson redisson;

    @Resource
    private WxPayService wxPayService;

    @Resource
    private RedisCache redisCache;

    @Resource
    private AppWxPayConfigMapper appWxPayConfigMapper;

    @Resource
    private AliPayService aliPayService;

    @Resource
    private AppGoodNumberMapper appGoodNumberMapper;

    @Resource
    private AppGoldTopupConfigMapper appGoldTopupConfigMapper;

    @Resource
    private AppOrderMapper appOrderMapper;


    @Resource
    private AppPersonalDressingMapper appPersonalDressingMapper;

    @Resource
    private PayService payService;

    @Resource
    private AppCommonService appCommonService;


    @Resource
    private AppAliPayConfigMapper appAliPayConfigMapper;

    @Resource
    private AppTopUpRewardsMapper appTopUpRewardsMapper;

    @Resource
    private AppUserPersonalDressingMapper appUserPersonalDressingMapper;


    @Resource
    private AppUserMapper appUserMapper;


    @Resource
    private JxPayService jxPayService;


    @Resource
    private AppSdPayConfigMapper appSdPayConfigMapper;

    @Resource
    private AppVipPriceMapper appVipPriceMapper;


    /**
     * 微信回调地址前缀
     */
    @Value("${pay.wx.notify-url-prefix}")
    private String notifyUrlPrefixByWx;

    /**
     * 微信回调地址前缀
     */
    @Value("${pay.wx.key-path}")
    private String keyPath;
    @Value("${pay.wx.public-key-path}")
    private String publicKeyPath;
    @Value("${pay.wx.serialNumber}")
    private String serialNumber;
    @Value("${spring.profiles.active}")
    private String activeProfile;

    /**
     * 获取支付渠道列表
     *
     * @return
     */
    public AjaxResult getPayChannelList() {

        List<Map<String, Object>> resultList = new ArrayList<>();


        AppConfig appConfig = appCommonService.getAppConfig();

        Integer sdPayConfig = appSdPayConfigMapper.isHaveEnableSdPayConfig();

        // 如果启用了精秀微信支付那就添加
        if (appConfig.getIsOpenSdWxPay().intValue() == WhetherTypeEnum.YES.getName() && null != sdPayConfig) {
            Map<String, Object> wxSdPayChannel = new HashMap<>();
            AppPayChannelTypeEnum payChannelTypeEnum = AppPayChannelTypeEnum.TYPE5;
            wxSdPayChannel.put("type", payChannelTypeEnum.getId());
            wxSdPayChannel.put("name", payChannelTypeEnum.getName());
            wxSdPayChannel.put("icoUrl", payChannelTypeEnum.getIcoUrl());
            resultList.add(wxSdPayChannel);
        }

        // 如果启用了精秀支付宝支付那就添加
        if (appConfig.getIsOpenSdZfbPay().intValue() == WhetherTypeEnum.YES.getName() && null != sdPayConfig) {
            Map<String, Object> zfbSdPayChannel = new HashMap<>();
            AppPayChannelTypeEnum payChannelTypeEnum = AppPayChannelTypeEnum.TYPE6;
            zfbSdPayChannel.put("type", payChannelTypeEnum.getId());
            zfbSdPayChannel.put("name", payChannelTypeEnum.getName());
            zfbSdPayChannel.put("icoUrl", payChannelTypeEnum.getIcoUrl());
            resultList.add(zfbSdPayChannel);
        }


        // 如果启用了官方微信支付那就添加
        if (appConfig.getIsOpenGfWxPay().intValue() == WhetherTypeEnum.YES.getName() && null != appWxPayConfigMapper.isHaveEnableWxPayConfig()) {
            Map<String, Object> wxPayChannel = new HashMap<>();
            AppPayChannelTypeEnum payChannelTypeEnum = AppPayChannelTypeEnum.TYPE1;
            wxPayChannel.put("type", payChannelTypeEnum.getId());
            wxPayChannel.put("name", payChannelTypeEnum.getName());
            wxPayChannel.put("icoUrl", payChannelTypeEnum.getIcoUrl());
            resultList.add(wxPayChannel);
        }

        // 如果启用了官方支付宝支付那就添加
        if (appConfig.getIsOpenGfZfbPay().intValue() == WhetherTypeEnum.YES.getName() && null != appAliPayConfigMapper.isHaveEnableAliPayConfig()) {
            Map<String, Object> zfbPayChannel = new HashMap<>();
            AppPayChannelTypeEnum payChannelTypeEnum = AppPayChannelTypeEnum.TYPE2;
            zfbPayChannel.put("type", payChannelTypeEnum.getId());
            zfbPayChannel.put("name", payChannelTypeEnum.getName());
            zfbPayChannel.put("icoUrl", payChannelTypeEnum.getIcoUrl());
            resultList.add(zfbPayChannel);
        }


        return AjaxResult.success(resultList);
    }

    /**
     * 根据用户ID获取用户信息
     *
     * @param recode
     * @return
     */
    public AjaxResult getUserInfoByRecode(String recode) {
        if (StringUtils.isBlank(recode)) {
            return AjaxResult.error("请输入用户ID");
        }

        AppUserEntity user = appUserMapper.selectAppUserByRecode(recode);
        if (null == user) {
            return AjaxResult.error("用户ID不存在");
        }

        Map<String, Object> result = new HashMap<>();


        result.put("recodeCode", user.getRecodeCode());
        result.put("nickName", user.getNickName());
        result.put("headPortrait", user.getHeadPortrait());
        result.put("phone", StringUtils.isBlank(user.getPhone()) ? "未绑定手机号" : DesensitizedUtil.mobilePhone(user.getPhone()));


        return AjaxResult.success(result);
    }

    /**
     * 获取首充奖励配置
     *
     * @param request
     * @return
     */
    public AjaxResult getOneTopUpRewardsConfig(HttpServletRequest request) {

        // 如果已经充值过，则不返回数据
        if (appOrderMapper.getUserIsTopUp(getUserId(request))) {
            return AjaxResult.success("查询成功", null);
        }

        // 如果没有开启首充奖励，则不返回数据
        AppTopUpRewards topUpRewards = appTopUpRewardsMapper.selectAppTopUpRewardsById();
        if (null == topUpRewards || topUpRewards.getIsEnable().intValue() != WhetherTypeEnum.YES.getName()) {
            return AjaxResult.success("查询成功", null);
        }

        // 封装返回的数据
        Map<String, Object> result = new HashMap<>();
        result.put("amount", topUpRewards.getAmount());// 充值金额
        List<AppUseInPersonalDressingVo> personalDressingList = new ArrayList<>();

        // 挂件
        if (!topUpRewards.getPendantId().equals(0L)) {
            AppUseInPersonalDressingVo pendant = appUserPersonalDressingMapper.getInfoById(topUpRewards.getPendantId());
            personalDressingList.add(pendant);
        }

        // 名牌
        if (!topUpRewards.getFamousBrandId().equals(0L)) {
            AppUseInPersonalDressingVo famousBrand = appUserPersonalDressingMapper.getInfoById(topUpRewards.getFamousBrandId());
            personalDressingList.add(famousBrand);
        }

        // 彩色昵称
        if (!topUpRewards.getColorNickNameId().equals(0L)) {
            AppUseInPersonalDressingVo colorNickName = appUserPersonalDressingMapper.getInfoById(topUpRewards.getColorNickNameId());
            personalDressingList.add(colorNickName);
        }

        // 坐骑
        if (!topUpRewards.getMountId().equals(0L)) {
            AppUseInPersonalDressingVo mount = appUserPersonalDressingMapper.getInfoById(topUpRewards.getMountId());
            personalDressingList.add(mount);
        }

        // 气泡框
        if (!topUpRewards.getChatFrameId().equals(0L)) {
            AppUseInPersonalDressingVo chatFrame = appUserPersonalDressingMapper.getInfoById(topUpRewards.getChatFrameId());
            personalDressingList.add(chatFrame);
        }

        // 头像框
        if (!topUpRewards.getMicFrame().equals(0L)) {
            AppUseInPersonalDressingVo micFrame = appUserPersonalDressingMapper.getInfoById(topUpRewards.getMicFrame());
            personalDressingList.add(micFrame);
        }

        result.put("personalDressingList", personalDressingList);
        return AjaxResult.success("查询成功", result);

    }

    /**
     * 获取金币充值配置列表
     *
     * @param iosBuy
     * @return
     */
    public AjaxResult getGoldTopupConfigList(Boolean iosBuy) {
        if (null == iosBuy) {
            iosBuy = false;
        }
        List<AppGoldTopupConfigVo> list = appGoldTopupConfigMapper.getGoldTopupConfigList(iosBuy ? WhetherTypeEnum.YES.getName() : WhetherTypeEnum.NO.getName());
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        return AjaxResult.success(list);
    }

    /**
     * h5充值
     *
     * @param vo
     * @param request
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult h5topUp(AppSubmitOrderVo vo, HttpServletRequest request) {
        if (StringUtils.isBlank(vo.getRecode())) {
            return AjaxResult.error("请输入用户ID");
        }


        AppUserEntity user = appUserMapper.selectAppUserByRecode(vo.getRecode());
        if (null == user) {
            return AjaxResult.error("用户ID不存在");
        }

        Long userId = user.getId();

        RLock lock = redisson.getLock("app:submitOrder:" + userId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(120, TimeUnit.SECONDS);
        // 入参校验
        if (null == vo.getOrderType() || vo.getOrderType().intValue() <= 0) {
            lock.unlock();
            return AjaxResult.error("参数【orderType】为空");
        }
        AppOrderTypeEnums orderTypeEnums = AppOrderTypeEnums.getEnum(vo.getOrderType().intValue());
        if (null == orderTypeEnums || orderTypeEnums.getId() != AppOrderTypeEnums.TYPE1.getId()) {
            lock.unlock();
            return AjaxResult.error("参数【orderType】错误");
        }

        if (null == vo.getGoodsId() || vo.getGoodsId().intValue() <= 0) {
            lock.unlock();
            return AjaxResult.error("参数【goodsId】为空");
        }

        if (null == vo.getPayChannelType()) {
            lock.unlock();
            return AjaxResult.error("参数【payChannelType】为空");
        }
        AppPayChannelTypeEnum payChannelTypeEnum = AppPayChannelTypeEnum.getEnum(vo.getPayChannelType().intValue());
        if (null == payChannelTypeEnum) {
            lock.unlock();
            return AjaxResult.error("参数【payChannelType】错误");
        }
        if (payChannelTypeEnum.getId() != AppPayChannelTypeEnum.TYPE2.getId() && payChannelTypeEnum.getId() != AppPayChannelTypeEnum.TYPE5.getId()) {
            lock.unlock();
            return AjaxResult.error("暂不支持该支付方式");
        }

        vo.setBuyNum(1L);// 默认购买数量为1


        Date time = new Date();
        // 生成订单编号
        String orderNo = OrderUtils.generateOrderNo();
        // 商品名称
        String goodsName = "";
        // 订单信息拼装
        AppOrder order = new AppOrder();
        order.setUserId(userId);
        order.setCreateTime(time);
        order.setOrderStatus((long) AppOrderStatusEnums.TYPE0.getId());// 待支付状态
        order.setIsDel(WhetherTypeEnum.NO.getName());
        order.setPayEndTime(DateUtils.addMinutes(time, RedisKeyConsts.orderPayValidMinute));
        order.setOrderNo(orderNo);
        order.setGoodsId(vo.getGoodsId());
        if (payChannelTypeEnum.getId() == AppPayChannelTypeEnum.TYPE2.getId()) {
            order.setPayChannelType((long) AppPayChannelTypeEnum.TYPE6.getId());
        } else {
            order.setPayChannelType(vo.getPayChannelType());
        }

        order.setOrderType(vo.getOrderType());

        Map<String, Object> result = new HashMap<>();

        // 校验充值项是否存在
        AppGoldTopupConfig goldTopupConfig = appGoldTopupConfigMapper.selectAppGoldTopupConfigById(vo.getGoodsId());
        if (null == goldTopupConfig || goldTopupConfig.getIsIos().intValue() == WhetherTypeEnum.YES.getName()) {
            lock.unlock();
            return AjaxResult.error("金币充值项不存在");
        }
        goodsName = "充值金币";
        // 设置订单价格
        order.setOrderPrice(goldTopupConfig.getPrice());
        order.setBuyNum(vo.getBuyNum());// 购买数量
        order.setGoodsInfoJson(JSON.toJSONString(goldTopupConfig));// 设置商品信息

        AppConfig appConfig = appCommonService.getAppConfig();

        BigDecimal userSumTopUpOrderPrice = appOrderMapper.getUserSumTopUpOrderPrice(userId).add(goldTopupConfig.getPrice());
        if (userSumTopUpOrderPrice.compareTo(appConfig.getRechargeLimitAmount()) >= 0 && user.getIsRealNameAuth().intValue() == WhetherTypeEnum.NO.getName()) {
            lock.unlock();
            return AjaxResult.error("请前往APP完成实名认证");
        }

        // 订单信息入库
        appOrderMapper.insertAppOrder(order);

        if (payChannelTypeEnum.getId() == AppPayChannelTypeEnum.TYPE2.getId()) {

            if (appConfig.getIsOpenSdZfbPay().intValue() != WhetherTypeEnum.YES.getName()) {
                lock.unlock();
                throw new AppException("该支付通道已关闭");
            }
            AppSdPayConfigVo appSdPayConfigVo = appSdPayConfigMapper.getEnableSdPayConfig();
            if (null == appSdPayConfigVo) {
                lock.unlock();
                throw new AppException("该支付通道已关闭");
            }

            // 订单结束时间
            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(order.getPayEndTime());
            String payResult = "";
            try {
                payResult = jxPayService.createAlipayOrder(time, goodsName, order.getOrderNo(), order.getOrderPrice(), endCalendar, request, appSdPayConfigVo);
                result.put("payUrl", payResult);
            } catch (Exception e) {
                log.info("创建精秀支付宝支付服务异常,userId:{},入参报文:{},msg:{}", userId, JSON.toJSONString(vo), e.getMessage());
                lock.unlock();
                throw new AppException(e.getMessage());
            }

            // 插入一个缓存，用于订单支付过期监听
            redisCache.setCacheObject(RedisKeyConsts.ORDER_PAY_TIME_OUT_LISTENING_KEY + order.getOrderNo(), order.getOrderNo(), RedisKeyConsts.orderPayValidMinute, TimeUnit.MINUTES);

        } else {
            if (appConfig.getIsOpenSdWxPay().intValue() != WhetherTypeEnum.YES.getName()) {
                lock.unlock();
                throw new AppException("该支付通道已关闭");
            }

            AppSdPayConfigVo appSdPayConfigVo = appSdPayConfigMapper.getEnableSdPayConfig();
            if (null == appSdPayConfigVo) {
                lock.unlock();
                throw new AppException("该支付通道已关闭");
            }

            // 订单结束时间
            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(order.getPayEndTime());
            String payResult = "";
            try {
                payResult = jxPayService.createWxOrder(time, goodsName, order.getOrderNo(), order.getOrderPrice(), endCalendar, request, appSdPayConfigVo);
            } catch (Exception e) {
                log.info("创建精秀微信支付服务异常,userId:{},入参报文:{},msg:{}", userId, JSON.toJSONString(vo), e.getMessage());
                lock.unlock();
                throw new AppException(e.getMessage());
            }

            result.put("payUrl", payResult);
            // 插入一个缓存，用于订单支付过期监听
            redisCache.setCacheObject(RedisKeyConsts.ORDER_PAY_TIME_OUT_LISTENING_KEY + order.getOrderNo(), order.getOrderNo(), RedisKeyConsts.orderPayValidMinute, TimeUnit.MINUTES);
        }

        lock.unlock();
        return AjaxResult.success("订单创建成功", result);
    }
    @Resource
    private AppConfigMapper appConfigMapper;

    /**
     * 统一提交订单
     *
     * @param vo
     * @param request
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult submitOrder(AppSubmitOrderVo vo, HttpServletRequest request) {
        AppUserEntity user = getUser(request);
        Long userId = user.getId();
        RLock lock = redisson.getLock("app:submitOrder:" + userId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(120, TimeUnit.SECONDS);
        // 入参校验
        if (null == vo.getOrderType() || vo.getOrderType().intValue() <= 0) {
            lock.unlock();
            return AjaxResult.error("参数【orderType】为空");
        }
        AppOrderTypeEnums orderTypeEnums = AppOrderTypeEnums.getEnum(vo.getOrderType().intValue());
        if (null == orderTypeEnums) {
            lock.unlock();
            return AjaxResult.error("参数【orderType】错误");
        }

        if (null == vo.getGoodsId() || vo.getGoodsId().intValue() <= 0) {
            lock.unlock();
            return AjaxResult.error("参数【goodsId】为空");
        }

        if (null == vo.getPayChannelType()) {
            lock.unlock();
            return AjaxResult.error("参数【payChannelType】为空");
        }
        AppPayChannelTypeEnum payChannelTypeEnum = AppPayChannelTypeEnum.getEnum(vo.getPayChannelType().intValue());
        if (null == payChannelTypeEnum) {
            lock.unlock();
            return AjaxResult.error("参数【payChannelType】错误");
        }

        if (null == vo.getBuyNum() || vo.getBuyNum().intValue() <= 0) {
            vo.setBuyNum(1L);// 默认购买数量为1
        }

        // 当前系统时间
        Calendar calendar = Calendar.getInstance();
        Date time = new Date();
        // 生成订单编号
        String orderNo = vo.getPayChannelType().equals(1L) ? OrderUtils.generateWxOrderNo() : OrderUtils.generateAlOrderNo();
        // 商品名称
        String goodsName = "";
        // 订单信息拼装
        AppOrder order = new AppOrder();
        order.setUserId(userId);
        order.setCreateTime(time);
        order.setOrderStatus((long) AppOrderStatusEnums.TYPE0.getId());// 待支付状态
        order.setIsDel(WhetherTypeEnum.NO.getName());
        order.setPayEndTime(DateUtils.addMinutes(time, RedisKeyConsts.orderPayValidMinute));
        order.setOrderNo(orderNo);
        order.setGoodsId(vo.getGoodsId());
        order.setPayChannelType(vo.getPayChannelType());
        order.setOrderType(vo.getOrderType());

        Map<String, Object> result = new HashMap<>();

        AppConfig appConfig = appConfigMapper.getAppConfig();

        // 充值金币订单
        if (orderTypeEnums.getId() == AppOrderTypeEnums.TYPE1.getId()) {

            if (payChannelTypeEnum.getId() != AppPayChannelTypeEnum.TYPE1.getId()
                    && payChannelTypeEnum.getId() != AppPayChannelTypeEnum.TYPE2.getId()
                    && payChannelTypeEnum.getId() != AppPayChannelTypeEnum.TYPE3.getId()
                    && payChannelTypeEnum.getId() != AppPayChannelTypeEnum.TYPE5.getId()
                    && payChannelTypeEnum.getId() != AppPayChannelTypeEnum.TYPE6.getId()) {
                lock.unlock();
                return AjaxResult.error("暂不支持该支付方式");
            }

            vo.setBuyNum(1L);
            // 校验充值项是否存在
            AppGoldTopupConfig goldTopupConfig = appGoldTopupConfigMapper.selectAppGoldTopupConfigById(vo.getGoodsId());
            if (null == goldTopupConfig) {
                lock.unlock();
                return AjaxResult.error("金币充值项不存在");
            }
            goodsName = "充值金币";
            // 设置订单价格
            order.setOrderPrice(goldTopupConfig.getPrice());
            order.setBuyNum(vo.getBuyNum());// 购买数量
            order.setGoodsInfoJson(JSON.toJSONString(goldTopupConfig));// 设置商品信息

            result.put("iosProductId", goldTopupConfig.getIosId());
            result.put("goldAmount", goldTopupConfig.getGoldNum());
            result.put("videoCardAmount", goldTopupConfig.getVideoCardAmount());

            // 充值金额大于2000RMB才提示实名认证
            BigDecimal userSumTopUpOrderPrice = appOrderMapper.getUserSumTopUpOrderPrice(userId).add(goldTopupConfig.getPrice());
            if (userSumTopUpOrderPrice.compareTo(appConfig.getRechargeLimitAmount()) >= 0 && user.getIsRealNameAuth() == WhetherTypeEnum.NO.getName()) {
                lock.unlock();
                return AjaxResult.noRealNameAuth("请先完成实名认证");
            }

            // 订单信息入库
            appOrderMapper.insertAppOrder(order);
        } else if (orderTypeEnums.getId() == AppOrderTypeEnums.TYPE2.getId()) {
            // 充值会员
            goodsName = "充值会员";

            // 支付方式
            if (payChannelTypeEnum.getId() != AppPayChannelTypeEnum.TYPE1.getId()
                    && payChannelTypeEnum.getId() != AppPayChannelTypeEnum.TYPE2.getId()
                    && payChannelTypeEnum.getId() != AppPayChannelTypeEnum.TYPE3.getId()
                    && payChannelTypeEnum.getId() != AppPayChannelTypeEnum.TYPE5.getId()
                    && payChannelTypeEnum.getId() != AppPayChannelTypeEnum.TYPE6.getId()) {
                lock.unlock();
                return AjaxResult.error("暂不支持该支付方式");
            }

            // 获取会员套餐
            AppVipPrice appVipPrice = appVipPriceMapper.selectById(vo.getGoodsId());
            if (appVipPrice == null) {
                lock.unlock();
                return AjaxResult.error("会员套餐不存在");
            }

            result.put("month", appVipPrice.getMonth());// 开通月份
            result.put("iosProductId", appVipPrice.getIosId());
            result.put("videoCardAmount", appVipPrice.getVideoCardAmount());

            // 设置订单价格
            order.setOrderPrice(appVipPrice.getPrice());
            order.setBuyNum(1L);
            order.setGoodsInfoJson(JSON.toJSONString(appVipPrice));// 设置商品信息

            appOrderMapper.insertAppOrder(order);

        } else if (orderTypeEnums.getId() == AppOrderTypeEnums.TYPE3.getId()) {
            // 购买商城商品
            if (payChannelTypeEnum.getId() != AppPayChannelTypeEnum.TYPE4.getId()) {
                lock.unlock();
                return AjaxResult.error("暂不支持该支付方式");
            }

            // 校验商城商品是否存在
            AppPersonalDressing mallGoods = appPersonalDressingMapper.selectAppPersonalDressingById(vo.getGoodsId());
            if (null == mallGoods || mallGoods.getIsDel().intValue() == WhetherTypeEnum.YES.getName()) {
                lock.unlock();
                return AjaxResult.error("商品不存在");
            }
            order.setOrderPrice(mallGoods.getGoldPrice());

            vo.setBuyNum(1L);
            goodsName = "购买商城商品";

            order.setBuyNum(vo.getBuyNum());// 购买数量
            order.setGoodsInfoJson(JSON.toJSONString(mallGoods));// 设置商品信息
            // 订单信息入库
            appOrderMapper.insertAppOrder(order);

        } else if (orderTypeEnums.getId() == AppOrderTypeEnums.TYPE8.getId()) {
            // 购买靓号
            if (payChannelTypeEnum.getId() != AppPayChannelTypeEnum.TYPE4.getId()) {
                lock.unlock();
                return AjaxResult.error("暂不支持该支付方式");
            }

            // 检测靓号购买锁
            Boolean buLock = redisCache.getCacheObject("app-goodNumber-" + vo.getGoodsId());
            if (null != buLock) {// 如果是锁住的那就直接返回出去
                lock.unlock();
                return AjaxResult.error("购买失败,该靓号已被其他用户锁定");
            }

            // 靓号购买上锁
            redisCache.setCacheObject("app-goodNumber-" + vo.getGoodsId(), true);

            AppGoodNumber goodNumber = appGoodNumberMapper.getGoodNumberById(vo.getGoodsId());
            if (null == goodNumber) {
                lock.unlock();
                try {
                    // 释放靓号锁
                    redisCache.deleteObject("app-goodNumber-" + vo.getGoodsId());
                } catch (Exception er) {
                }
                return AjaxResult.error("该靓号不存在或已删除");
            }

            if (goodNumber.getStatus() == AppGoodNumberStatusTypeEnum.TYPE0.getName()) {
                lock.unlock();
                try {
                    // 释放靓号锁
                    redisCache.deleteObject("app-goodNumber-" + vo.getGoodsId());
                } catch (Exception er) {
                }
                return AjaxResult.error("该靓号暂未开售");
            }

            if (goodNumber.getStatus() == AppGoodNumberStatusTypeEnum.TYPE2.getName()) {
                lock.unlock();
                try {
                    // 释放靓号锁
                    redisCache.deleteObject("app-goodNumber-" + vo.getGoodsId());
                } catch (Exception er) {
                }
                return AjaxResult.error("该靓号已售出");
            }

            goodsName = "购买靓号";
            // 设置订单价格
            order.setOrderPrice(goodNumber.getMasonryPrice());
            order.setBuyNum(1L);// 购买数量
            order.setGoodsInfoJson(JSON.toJSONString(goodNumber));
            // 订单信息入库
            appOrderMapper.insertAppOrder(order);

        }
        result.put("orderId", order.getId());// 订单id
        result.put("orderNo", orderNo);// 订单编号
        result.put("orderType", order.getOrderType());// 订单类型
        // 支付
        if (vo.getPayChannelType().intValue() == AppPayChannelTypeEnum.TYPE1.getId()) {
            // 微信支付
            if (appConfig.getIsOpenGfWxPay() != WhetherTypeEnum.YES.getName()) {
                lock.unlock();
                throw new AppException("该支付通道已关闭");
            }

            // 获取启用的微信支付配置信息
            AppWxPayConfigVo appWxPayConfigVo = appWxPayConfigMapper.getEnableWxPayConfig();
            if (null == appWxPayConfigVo) {
                lock.unlock();
                throw new AppException("该支付通道已关闭");
            }

            WxPayAppOrderResult wxPayAppOrderResult = null;
            //                wxPayAppOrderResult = createWxOrder(goodsName, order.getOrderNo(), order.getOrderPrice(), calendar, appWxPayConfigVo);
//            Config config =
//                    new RSAAutoCertificateConfig.Builder()
//                            .merchantId(appWxPayConfigVo.getMchId())
//                            // 使用 com.wechat.pay.java.core.util 中的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
//                            .privateKeyFromPath(keyPath)
//                            .merchantSerialNumber(serialNumber)
//                            .apiV3Key(appWxPayConfigVo.getMchKey())
//                            .build();

            Config config =
                    new RSAPublicKeyConfig.Builder()
                            .merchantId(appWxPayConfigVo.getMchId()) //微信支付的商户号
                            .privateKeyFromPath(keyPath) // 商户API证书私钥的存放路径
                            .publicKeyFromPath(publicKeyPath) //微信支付公钥的存放路径
                            .publicKeyId("PUB_KEY_ID_0117160461292025050700381855001000") //微信支付公钥ID
                            .merchantSerialNumber(serialNumber) //商户API证书序列号
                            .apiV3Key(appWxPayConfigVo.getMchKey()) //APIv3密钥
                            .build();
            // 初始化服务
            AppServiceExtension service = new AppServiceExtension.Builder().config(config).build();
            BigDecimal orderPrice = order.getOrderPrice();
            log.info("订单价格:{}", orderPrice);
            orderPrice = orderPrice.multiply(new BigDecimal("100"));
            BigInteger bigInteger = orderPrice.toBigInteger();
            // 构造微信订单
            PrepayRequest request2 = new PrepayRequest();
            request2.setAppid(appWxPayConfigVo.getAppId());
            request2.setMchid(appWxPayConfigVo.getMchId());
            request2.setDescription(goodsName);
            request2.setOutTradeNo(orderNo);
            request2.setNotifyUrl(StringUtils.format(notifyUrlPrefixByWx, appWxPayConfigVo.getId()));
            Amount amount = new Amount();
            amount.setCurrency("CNY");
            amount.setTotal(bigInteger.intValueExact());
            request2.setAmount(amount);


            log.info("请求微信支付入参:{}", JsonUtils.objectToJson(request2));
            PrepayWithRequestPaymentResponse response = service.prepayWithRequestPayment(request2);
            log.info("请求微信支付出参:{}", JsonUtils.objectToJson(response));


            // 设置微信支付数据
            result.put("payInfo", response);
            log.info("发起支付prepay", JsonUtils.objectToJson(response));
            // 插入一个缓存，用于订单支付过期监听
            redisCache.setCacheObject(RedisKeyConsts.ORDER_PAY_TIME_OUT_LISTENING_KEY + order.getOrderNo(), order.getOrderNo(), RedisKeyConsts.orderPayValidMinute, TimeUnit.MINUTES);

        } else if (vo.getPayChannelType().intValue() == AppPayChannelTypeEnum.TYPE3.getId()) {
            // 苹果内购
            log.info("用户调用苹果支付，用户id：{}， 订单id：{}", userId, order.getId());
            // 插入一个缓存，用于订单支付过期监听
            redisCache.setCacheObject(RedisKeyConsts.ORDER_PAY_TIME_OUT_LISTENING_KEY + order.getOrderNo(), order.getOrderNo(), RedisKeyConsts.orderPayValidMinute, TimeUnit.MINUTES);
        }else if (vo.getPayChannelType().intValue() == AppPayChannelTypeEnum.TYPE5.getId()) {
            // 微信支付-JX

            if (appConfig.getIsOpenSdWxPay().intValue() != WhetherTypeEnum.YES.getName()) {
                lock.unlock();
                throw new AppException("该支付通道已关闭");
            }

            AppSdPayConfigVo appSdPayConfigVo = appSdPayConfigMapper.getEnableSdPayConfig();
            if (null == appSdPayConfigVo) {
                lock.unlock();
                throw new AppException("该支付通道已关闭");
            }


            // 订单结束时间
            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(order.getPayEndTime());
            String payResult = "";
            try {
                payResult = jxPayService.createWxOrder(time, goodsName, order.getOrderNo(), order.getOrderPrice(), endCalendar, request, appSdPayConfigVo);
            } catch (Exception e) {
                log.info("创建精秀微信支付服务异常,userId:{},入参报文:{},msg:{}", userId, JSON.toJSONString(vo), e.getMessage());
                lock.unlock();
                throw new AppException(e.getMessage());
            }

            // 设置支付数据
            Map<String, Object> payData = new HashMap<>();
            payData.put("key", payResult);
            result.put("payInfo", payData);
            // 插入一个缓存，用于订单支付过期监听
            redisCache.setCacheObject(RedisKeyConsts.ORDER_PAY_TIME_OUT_LISTENING_KEY + order.getOrderNo(), order.getOrderNo(), RedisKeyConsts.orderPayValidMinute, TimeUnit.MINUTES);

        } else if (vo.getPayChannelType().intValue() == AppPayChannelTypeEnum.TYPE6.getId()) {
            // 支付宝支付-JX

            if (appConfig.getIsOpenSdZfbPay() != WhetherTypeEnum.YES.getName()) {
                lock.unlock();
                throw new AppException("该支付通道已关闭");
            }
            AppSdPayConfigVo appSdPayConfigVo = appSdPayConfigMapper.getEnableSdPayConfig();
            if (null == appSdPayConfigVo) {
                lock.unlock();
                throw new AppException("该支付通道已关闭");
            }

            // 订单结束时间
            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(order.getPayEndTime());
            String payResult = "";
            try {
                payResult = jxPayService.createAlipayOrder(time, goodsName, order.getOrderNo(), order.getOrderPrice(), endCalendar, request, appSdPayConfigVo);
            } catch (Exception e) {
                log.info("创建精秀支付宝支付服务异常,userId:{},入参报文:{},msg:{}", userId, JSON.toJSONString(vo), e.getMessage());
                lock.unlock();
                throw new AppException(e.getMessage());
            }

            // 设置支付数据
            Map<String, Object> payData = new HashMap<>();
            payData.put("key", payResult);
            result.put("payInfo", payData);
            // 插入一个缓存，用于订单支付过期监听
            redisCache.setCacheObject(RedisKeyConsts.ORDER_PAY_TIME_OUT_LISTENING_KEY + order.getOrderNo(), order.getOrderNo(), RedisKeyConsts.orderPayValidMinute, TimeUnit.MINUTES);

        } else if (vo.getPayChannelType().intValue() == AppPayChannelTypeEnum.TYPE4.getId()) {
            // 金币支付

            if (user.getGoldBalance().compareTo(order.getOrderPrice()) < 0) {
                try {
                    if (orderTypeEnums.getId() == AppOrderTypeEnums.TYPE8.getId()) {// 如果是购买靓号，那就删除锁
                        // 释放靓号锁
                        redisCache.deleteObject("app-goodNumber-" + vo.getGoodsId());
                    }
                } catch (Exception er) {
                }

                lock.unlock();
                throw new AppException("金币余额不足");
            }

            // 处理购买和余额扣除
            try {
                payService.paymentBuy(order);
                result.put("payInfo", null);
                try {
                    if (orderTypeEnums.getId() == AppOrderTypeEnums.TYPE8.getId()) {// 如果是购买靓号，那就删除锁
                        // 释放靓号锁
                        redisCache.deleteObject("app-goodNumber-" + vo.getGoodsId());
                    }
                } catch (Exception er) {
                }

                lock.unlock();

                return AjaxResult.success("购买成功", result);
            } catch (Exception e) {
                try {
                    if (orderTypeEnums.getId() == AppOrderTypeEnums.TYPE8.getId()) {// 如果是购买靓号，那就删除锁
                        // 释放靓号锁
                        redisCache.deleteObject("app-goodNumber-" + vo.getGoodsId());
                    }
                } catch (Exception er) {
                }

                lock.unlock();
                if (e instanceof AppException) {
                    throw new AppException(e.getMessage());
                } else {
                    log.info("金币支付服务异常,userId:{},入参报文:{},msg:{}", userId, JSON.toJSONString(vo), e.getMessage());
                    throw new AppException("支付失败,系统错误");
                }
            }

        } else if (vo.getPayChannelType().intValue() == AppPayChannelTypeEnum.TYPE2.getId()) {
            // 支付宝支付

            if (appConfig.getIsOpenGfZfbPay() != WhetherTypeEnum.YES.getName()) {
                lock.unlock();
                throw new AppException("该支付通道已关闭");
            }

            // 获取启用中的支付宝配置信息
            AppAliPayConfigVo appAliPayConfig = appAliPayConfigMapper.getEnableAliPayConfig();
            if (null == appAliPayConfig) {
                lock.unlock();
                throw new AppException("该支付通道已关闭");
            }

            // 支付参数
            Map<String, Object> data = new HashMap<>();
            // 订单结束时间
            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(order.getPayEndTime());
            if (!isH5Request()) {// 非h5请求
                // 创建支付宝支付
                Object aliPayOrderResult = null;
                try {
                    aliPayOrderResult = aliPayService.createAliPay(goodsName, order.getOrderNo(), order.getOrderPrice(), endCalendar, appAliPayConfig);
                } catch (AlipayApiException e) {
                    log.error("创建支付宝服务异常,userId:{},入参报文:{},msg:{}", userId, JSON.toJSONString(vo), e.getMessage());
                    lock.unlock();
                    throw new AppException("支付宝服务异常");
                }

                data.put("key", aliPayOrderResult);
                result.put("payInfo", data);
            } else {// h5请求
                try {
                    String payUrl = aliPayService.createAliPayByH5(goodsName, order.getOrderNo(), order.getOrderPrice(), endCalendar, order.getId() + "&token=" + user.getToken(), appAliPayConfig);
                    result.put("payUrl", payUrl);
                } catch (AlipayApiException e) {
                    log.info("创建支付宝H5服务异常,userId:{},入参报文:{},msg:{}", userId, JSON.toJSONString(vo), e.getMessage());
                    lock.unlock();
                    throw new AppException("支付宝服务异常");
                }
            }

            // 插入一个缓存，用于订单支付过期监听
            redisCache.setCacheObject(RedisKeyConsts.ORDER_PAY_TIME_OUT_LISTENING_KEY + order.getOrderNo(), order.getOrderNo(), RedisKeyConsts.orderPayValidMinute, TimeUnit.MINUTES);

        }

        lock.unlock();
        return AjaxResult.success("订单创建成功", result);
    }

    /**
     * 获取订单状态
     *
     * @param request
     * @param orderId
     * @return
     */
    public AjaxResult getOrderStatus(HttpServletRequest request, String orderId) {
        if (null == orderId) {
            return AjaxResult.error("参数【orderId】为空");
        }
        Long userId = getUserId(request);
        AppOrder order = appOrderMapper.selectAppOrderById(orderId);
        if (null == order || !order.getUserId().equals(userId)) {
            return AjaxResult.error("订单不存在");
        }

        return AjaxResult.success("查询成功", order.getOrderStatus());
    }

    /**
     * 创建微信支付订单
     *
     * @param goodsName
     * @param orderNo
     * @param price
     * @param calendar
     * @param appWxPayConfigVo
     * @return
     * @throws WxPayException
     */
    public WxPayAppOrderResult createWxOrder(String goodsName, String orderNo, BigDecimal price, Calendar calendar, AppWxPayConfigVo appWxPayConfigVo) throws WxPayException {

        WxPayConfig wxPayConfigVo = new WxPayConfig();
        wxPayConfigVo.setAppId(appWxPayConfigVo.getAppId());
        wxPayConfigVo.setMchId(appWxPayConfigVo.getMchId());
        wxPayConfigVo.setMchKey(appWxPayConfigVo.getMchKey());
        wxPayConfigVo.setNotifyUrl(StringUtils.format(notifyUrlPrefixByWx, appWxPayConfigVo.getId()));
        wxPayConfigVo.setSignType(WxPayConstants.SignType.HMAC_SHA256);
        wxPayConfigVo.setTradeType("APP");
        wxPayService.setConfig(wxPayConfigVo);

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();


        // 拼接微信app支付参数
        WxPayUnifiedOrderRequest wxPayUnifiedOrderRequest = new WxPayUnifiedOrderRequest();

        // 商品名称
        wxPayUnifiedOrderRequest.setBody(goodsName);
        // 订单编号
        wxPayUnifiedOrderRequest.setOutTradeNo(orderNo);
        // 订单金额
        wxPayUnifiedOrderRequest.setTotalFee(price.multiply(new BigDecimal(100)).intValue());
        wxPayUnifiedOrderRequest.setSpbillCreateIp(null == request ? "127.0.0.1" : IpUtils.getIpAddr(request));
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        // 交易起始时间
        wxPayUnifiedOrderRequest.setTimeStart(formatter.format(calendar.getTime()));
        // 交易结束时间 (当前15分钟后的时间-1分钟也就是14分钟，避免在到期前几秒支付成功后，redis监听过期订单时该订单被删除，系统订单有效是15分钟，传给微信的是15-1为14分钟)
        wxPayUnifiedOrderRequest.setTimeExpire(DateUtils.getMinuteAfter(calendar, RedisKeyConsts.orderPayValidMinute - 1));
        return wxPayService.createOrder(wxPayUnifiedOrderRequest);
    }

    /**
     * 退款
     *
     * @param orderNo 订单号
     * @return {@link AjaxResult }
     */
    public AjaxResult refund(String orderNo) {
        if (!"dev".equals(activeProfile)) {
            return AjaxResult.error("dev开发环境才可以退费");
        }

        AppOrder appOrder = appOrderMapper.selectAppOrderByOrderNo(orderNo);
        if (appOrder == null) {
            return AjaxResult.error("订单不存在");
        }
        
        // 修复支付渠道判断逻辑
        if (appOrder.getPayChannelType().intValue() == AppPayChannelTypeEnum.TYPE1.getId()) {
            return wxOrderRefund(orderNo, appOrder);
        }

        if (appOrder.getPayChannelType().intValue() == AppPayChannelTypeEnum.TYPE2.getId()) {
            return aliOrderRefund(orderNo, appOrder);
        }

        return AjaxResult.error("尚未支持的退款渠道方式");
    }

    private AjaxResult wxOrderRefund(String orderNo, AppOrder appOrder) {
        // 获取启用的微信支付配置信息
        AppWxPayConfigVo appWxPayConfigVo = appWxPayConfigMapper.getEnableWxPayConfig();

        CreateRequest request = new CreateRequest();

        request.setOutTradeNo(orderNo);
        request.setOutRefundNo("TK" + orderNo);
        request.setReason("后台退费");

        AmountReq amountReq = new AmountReq();
        amountReq.setRefund(appOrder.getOrderPrice().multiply(BigDecimal.valueOf(100)).longValue());
        amountReq.setTotal(appOrder.getOrderPrice().multiply(BigDecimal.valueOf(100)).longValue());
        amountReq.setCurrency("CNY");
        request.setAmount(amountReq);

//        Config config = new RSAAutoCertificateConfig.Builder()
//                .merchantId(appWxPayConfigVo.getMchId())
//                .privateKeyFromPath(keyPath)
//                .merchantSerialNumber(serialNumber)
//                .apiV3Key(appWxPayConfigVo.getMchKey())
//                .build();

        Config config =
                new RSAPublicKeyConfig.Builder()
                        .merchantId(appWxPayConfigVo.getMchId()) //微信支付的商户号
                        .privateKeyFromPath(keyPath) // 商户API证书私钥的存放路径
                        .publicKeyFromPath(publicKeyPath) //微信支付公钥的存放路径
                        .publicKeyId("PUB_KEY_ID_0117160461292025050700381855001000") //微信支付公钥ID
                        .merchantSerialNumber(serialNumber) //商户API证书序列号
                        .apiV3Key(appWxPayConfigVo.getMchKey()) //APIv3密钥
                        .build();

        RefundService refundService = new RefundService.Builder().config(config).build();
        try {
            Refund refund = refundService.create(request);
            return AjaxResult.success(refund);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    private AjaxResult aliOrderRefund(String orderNo, AppOrder appOrder) {
        // 获取启用中的支付宝配置信息
        AppAliPayConfigVo appAliPayConfig = appAliPayConfigMapper.getEnableAliPayConfig();
        try {
            AlipayTradeRefundResponse refund = aliPayService.refund(orderNo, appOrder.getOrderPrice(), "后台退费", appAliPayConfig);
            return AjaxResult.success(refund);
        } catch (AlipayApiException e) {
            log.error("支付宝退款异常: {}", e.getMessage());
            return AjaxResult.error(e.getMessage());
        }
    }
}
