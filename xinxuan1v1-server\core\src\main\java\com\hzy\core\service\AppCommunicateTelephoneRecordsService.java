package com.hzy.core.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzy.core.config.RedisCache;
import com.hzy.core.constant.CacheConstants;
import com.hzy.core.entity.*;
import com.hzy.core.enums.*;
import com.hzy.core.mapper.*;
import com.hzy.core.model.bo.TelResult;
import com.hzy.core.utils.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 通话记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-06
 */
@Service
public class AppCommunicateTelephoneRecordsService extends ServiceImpl<AppCommunicateTelephoneRecordsMapper, AppCommunicateTelephoneRecords> {
    private static final Logger log = LoggerFactory.getLogger(AppCommunicateTelephoneRecordsService.class);
    
    @Resource
    private AppCommunicateTelephoneRecordsMapper appCommunicateTelephoneRecordsMapper;
    @Resource
    private AppUserMapper appUserMapper;
    @Resource
    private AppUserGoldBillMapper appUserGoldBillMapper;
    @Resource
    private AppUserGoldBillService appUserGoldBillService;
    @Resource
    private AppGiftIncomeConfigService appGiftIncomeConfigService;
    @Resource
    private AppChattingRecordsService appChattingRecordsService;
    @Resource
    private AppUserPointsBillMapper appUserPointsBillMapper;
    @Resource
    private AppGiftService2 appGiftService2;
    @Resource
    private AppConfigMapper appConfigMapper;
    @Resource
    private ChannelCommissionRecordService channelCommissionRecordService;
    @Resource
    private AppUserCommunicateTelephoneConfigService appUserCommunicateTelephoneConfigService;
    @Resource
    private RedisCache redisCache;
    @Resource
    private AppGuildMapper appGuildMapper;
    @Resource
    private AppGuildMemberService appGuildMemberService;
    @Resource
    private AppPrivateLetterListService appPrivateLetterListService;
    @Resource
    private AppGuildMemberMapper appGuildMemberMapper;
    @Resource
    private Redisson redisson;
    @Resource
    private AppUserVideoCardMapper appUserVideoCardMapper;
    @Resource
    private AppUserVideoCardService appUserVideoCardService;

    /**
     * 获取挂断电话结果
     *
     * @param userId                      挂断用户id
     * @param communicateTelephoneRecords 电话记录
     * @return {@link TelResult }
     */
    public TelResult getTelResult(Long userId, AppCommunicateTelephoneRecords communicateTelephoneRecords) {
        // 创建分布式锁，防止重复扣费
        RLock lock = redisson.getLock("app:telephone:end:" + communicateTelephoneRecords.getId());
        // 如果锁已被其他线程获取，说明该记录正在被处理中，直接返回空结果避免重复处理
        if (lock.isLocked()) {
            log.warn("通话记录ID[{}]正在被处理中，跳过本次处理", communicateTelephoneRecords.getId());
            return null;
        }
        
        try {
            // 获取锁，并设置60秒自动释放，避免死锁
            lock.lock(60, TimeUnit.SECONDS);
            
            // 再次检查记录状态，确保没有被其他线程处理过
            AppCommunicateTelephoneRecords currentRecord = appCommunicateTelephoneRecordsMapper.selectAppCommunicateTelephoneRecordsById(communicateTelephoneRecords.getId());
            if (currentRecord.getStatus() == AppCommunicateTelephoneStatusTypeEnums.TYPE3.getId()) {
                log.info("通话记录ID[{}]已经被处理过，状态为已挂断", communicateTelephoneRecords.getId());
                return null;
            }

            // 移除视频卡结算逻辑，简化为金币结算
            boolean isUseVideoCard = false;

            // 当前通话时长
            Date time = new Date();
            LocalDateTime now = LocalDateTime.ofInstant(time.toInstant(), ZoneId.systemDefault());
            long callMinutes = (time.getTime() - communicateTelephoneRecords.getConnectTime().getTime()) / 1000; // 秒
            long minute = (long) Math.ceil(callMinutes / 60.0); // 分钟
            
            // 获取发起用户和接收用户信息
            AppUserEntity initiateUser = appUserMapper.selectAppUserById(communicateTelephoneRecords.getInitiateUserId());
            AppUserEntity receiveUser = appUserMapper.selectAppUserById(communicateTelephoneRecords.getReceiveUserId());
            
            // 用户信息日志
            log.info("通话ID[{}] 发起用户ID[{}] 接收用户ID[{}] 发起用户性别[{}] 接收用户性别[{}]", 
                    communicateTelephoneRecords.getId(), 
                    communicateTelephoneRecords.getInitiateUserId(), 
                    communicateTelephoneRecords.getReceiveUserId(), 
                    initiateUser.getSex(), receiveUser.getSex());
            
            // 判断谁应该扣费：异性通话扣男性的钱，同性通话扣发起用户的钱
            AppUserEntity payingUser;
            AppUserEntity rateUser; // 用于确定费率的用户（异性通话时始终使用女性用户）
            AppUserEntity incomeUser; // 收益归属用户
            
            if (initiateUser.getSex() != null && receiveUser.getSex() != null 
                && initiateUser.getSex() != receiveUser.getSex()) {
                // 异性通话：扣男性的钱，费率按女性的标准，收益给女性
                if (initiateUser.getSex() == AppUserSexTypeEnums.TYPE0.getId()) { // 发起者是男性
                    payingUser = initiateUser; // 扣男性的钱
                    rateUser = receiveUser;    // 按接收用户（女性）的费率
                    incomeUser = receiveUser;  // 收益给女性
                } else { // 发起者是女性
                    payingUser = receiveUser;  // 扣男性（接收者）的钱
                    rateUser = initiateUser;   // 按发起用户（女性）的费率
                    incomeUser = initiateUser; // 收益给女性发起方
                }
            } else {
                // 同性通话：扣发起用户的钱，按接收用户的费率，收益给接收用户
                payingUser = initiateUser;
                rateUser = receiveUser;
                incomeUser = receiveUser;
            }
            
            // 计算实际收费金额（统一使用费率用户计算）
            final BigDecimal actualCallsGold = appUserPointsBillMapper.sumTotalAmountByObjectId(communicateTelephoneRecords.getId());
            
            // 费用计算日志
            log.info("通话ID[{}] 通话时长[{}]分钟 扣费用户ID[{}] 费率用户ID[{}] 收益用户ID[{}] 实际收费金额[{}]", 
                    communicateTelephoneRecords.getId(), minute, payingUser.getId(), rateUser.getId(), 
                    incomeUser.getId(), actualCallsGold);

            // 更新通话记录-改为挂断
            // 从app_user_points_bill表中汇总实际消费金币（根据需求文档要求）
            communicateTelephoneRecords.setConsumptionGold(actualCallsGold);
            log.info("通话ID[{}] 金币结算: 从钻石账单汇总的实际消费金币[{}]",
                    communicateTelephoneRecords.getId(), actualCallsGold);
            communicateTelephoneRecords.setStatus(AppCommunicateTelephoneStatusTypeEnums.TYPE3.getId());
            communicateTelephoneRecords.setHangUpTime(time);
            communicateTelephoneRecords.setHangUpUserId(userId);
            communicateTelephoneRecords.setUpdateTime(time);
            appCommunicateTelephoneRecordsMapper.updateAppCommunicateTelephoneRecords(communicateTelephoneRecords);
            
            // 该用户是否有免费通话1分钟
            if (payingUser.getIsFreeVoice() == 0) {
                payingUser.setIsFreeVoice(1);
                appUserMapper.updateAppUser(payingUser);
            }

            // 挂断电话时不再重复扣费，金币扣费已在每分钟扣费逻辑中完成
            // 这里只处理渠道分佣和公会长收益的账单记录
            AppConfig appConfig = appConfigMapper.getAppConfig();
            BigDecimal amountOfIncome = BigDecimal.ZERO;

            log.info("挂断电话处理 - 通话ID[{}] 不再重复扣费，只处理渠道分佣和公会长收益",
                    communicateTelephoneRecords.getId());

            if (actualCallsGold.compareTo(new BigDecimal("0")) > 0) {
                // 升级贡献等级（基于实际付费用户）
                appGiftService2.levelUp(payingUser.getId(), actualCallsGold, time);
                // 升级魅力等级（基于收益用户）
                appGiftService2.charmlevelUp(incomeUser.getId(), actualCallsGold, time);

                // 增加该用户的会长结算收益
                // 公会长的收益（基于收益用户）
                BigDecimal guildLeadScale = appGuildMapper.getGuildLeadScale(incomeUser.getId());
                if (guildLeadScale != null && guildLeadScale.compareTo(BigDecimal.ZERO) > 0) {
                    // 收益
                    BigDecimal divide = actualCallsGold.multiply(guildLeadScale).multiply(new BigDecimal("0.5")).multiply(new BigDecimal("0.1"))
                            .divide(appConfig.getOnePointsEqGold(), 2, RoundingMode.HALF_UP);

                    Long guildLeadId = appGuildMapper.getGuildLeadIdByUserId(incomeUser.getId());
                    AppUserPointsBill guildLead = new AppUserPointsBill();
                    guildLead.setUserId(guildLeadId);
                    guildLead.setCreateTime(time);
                    guildLead.setBillType((long) AppPointsBillTypeEnums.TYPE25.getId());
                    guildLead.setObjectId(communicateTelephoneRecords.getId());
                    guildLead.setAmount(divide);
                    guildLead.setTotalAmount(actualCallsGold);
                    guildLead.setIsDel(WhetherTypeEnum.NO.getName());
                    guildLead.setRemarksMsg(StringUtils.format(AppPointsBillTypeEnums.TYPE25.getDesc(), incomeUser.getNickName()));
                    guildLead.setGuildId(appGuildMemberMapper.getGuildIdByUserId(guildLeadId));
                    appUserPointsBillMapper.insertAppUserPointsBill(guildLead);

                    // 添加公会长钻石
                    appUserMapper.addUserPointsBalance(guildLeadId, divide);
                }

                // 加该用户的上级渠道结算收益 - 基于收益用户进行分佣
                // 创建渠道结算用的记录副本，避免修改原始记录
                AppCommunicateTelephoneRecords channelRecord = new AppCommunicateTelephoneRecords();
                channelRecord.setId(communicateTelephoneRecords.getId());
                channelRecord.setInitiateUserId(communicateTelephoneRecords.getInitiateUserId());
                channelRecord.setReceiveUserId(communicateTelephoneRecords.getReceiveUserId());
                channelRecord.setType(communicateTelephoneRecords.getType());
                channelRecord.setStatus(communicateTelephoneRecords.getStatus());
                channelRecord.setConnectTime(communicateTelephoneRecords.getConnectTime());
                channelRecord.setHangUpTime(communicateTelephoneRecords.getHangUpTime());
                channelRecord.setHangUpUserId(communicateTelephoneRecords.getHangUpUserId());
                channelRecord.setCreateTime(communicateTelephoneRecords.getCreateTime());
                channelRecord.setUpdateTime(communicateTelephoneRecords.getUpdateTime());
                // 使用实际消费金币
                channelRecord.setConsumptionGold(actualCallsGold);
                
                channelCommissionRecordService.saveChannelTelRecordsForFemale(incomeUser, channelRecord);
                // 挂断时不再计算收益，收益已在每分钟扣费时计算
                amountOfIncome = BigDecimal.ZERO;
            }
            setChatTelRecord(communicateTelephoneRecords, time);
            return new TelResult(communicateTelephoneRecords, amountOfIncome);
        } catch (Exception e) {
            log.error("结束通话处理异常，通话ID：{}", communicateTelephoneRecords.getId(), e);
            throw e;
        } finally {
            // 确保锁被释放
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @NotNull
    public BigDecimal getCallsGold(AppCommunicateTelephoneRecords communicateTelephoneRecords, long callMinutes, long minute, Long rateUserId) {
        // 获取费率用户的通话配置,获取每分钟通话金额
        AppUserCommunicateTelephoneConfig rateUserCommunicateTelephoneConfig = appUserCommunicateTelephoneConfigService.getUserCommunicateTelephoneConfigByUserId(rateUserId);
        BigDecimal minutesGold = getMinutesGold(communicateTelephoneRecords, rateUserCommunicateTelephoneConfig);

        // 更新接收用户的通话时长统计（仍然需要更新接收用户的统计数据）
        AppUserCommunicateTelephoneConfig receiveUserConfig = appUserCommunicateTelephoneConfigService.getUserCommunicateTelephoneConfigByUserId(communicateTelephoneRecords.getReceiveUserId());
        if (communicateTelephoneRecords.getType() == AppCommunicateTelephoneTypeEnums.TYPE2.getId()
                || communicateTelephoneRecords.getType() == AppCommunicateTelephoneTypeEnums.TYPE4.getId()) {
            receiveUserConfig.setVideoTotalTime((int) (receiveUserConfig.getVideoTotalTime() + callMinutes));
        } else {
            receiveUserConfig.setVoiceTotalTime((int) (receiveUserConfig.getVoiceTotalTime() + callMinutes));
        }
        appUserCommunicateTelephoneConfigService.update(receiveUserConfig);

        // 计算当前通话金额= 时间(分钟)*每分钟通话金额
        return minutesGold.multiply(new BigDecimal(minute));
    }

    /**
     * 获取每分钟通话金额
     * <AUTHOR>
     * @date 2025/5/26 下午4:43
     */
    public BigDecimal getMinutesGold(AppCommunicateTelephoneRecords communicateTelephoneRecords, AppUserCommunicateTelephoneConfig receiveUserCommunicateTelephoneConfig) {
        List<SysDictData> dictDataList = redisCache.getCacheObject(CacheConstants.MATCHING_CALL_COST_KEY);

        Map<String, String> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(dictDataList)) {
            dictDataList.forEach(item -> {
                if (item.getRemark().equals(String.valueOf(AppCommunicateTelephoneTypeEnums.TYPE3.getId()))) {
                    map.put("voice", item.getDictValue());
                }
                if (item.getRemark().equals(String.valueOf(AppCommunicateTelephoneTypeEnums.TYPE4.getId()))) {
                    map.put("video", item.getDictValue());
                }
            });
        } else {
            // 默认值
            map.put("voice", "10");
            map.put("video", "30");
        }
        // 每分钟消费的金币
        BigDecimal minutesGold;
        switch (communicateTelephoneRecords.getType()) {
            case 1:
                // 获取语音通话每分钟消费的金币
                minutesGold = receiveUserCommunicateTelephoneConfig.getVoiceMinutesGold();
                break;
            case 2:
                // 获取视频通话每分钟消费的金币
                minutesGold = receiveUserCommunicateTelephoneConfig.getVideoMinutesGold();
                break;
            case 3:
                // 匹配语音通话固定10金币每分钟
                minutesGold = new BigDecimal(map.get("voice"));
                break;
            default:
                // 匹配视频通话固定30金币每分钟
                minutesGold = new BigDecimal(map.get("video"));
                break;
        }
        return minutesGold;
    }

    public void setChatTelRecord(AppCommunicateTelephoneRecords communicateTelephoneRecords, Date time) {
        AppChattingRecords appChattingRecords = new AppChattingRecords();
        appChattingRecords.setSendUserId(communicateTelephoneRecords.getInitiateUserId());
        appChattingRecords.setReceiveUserId(communicateTelephoneRecords.getReceiveUserId());
        appChattingRecords.setSendTime(time);
        appChattingRecords.setIsRead(1);

        // 根据通话类型设置消息类型和内容描述
        AppSendMsgTypeEnums msgType = communicateTelephoneRecords.getType() == AppCommunicateTelephoneTypeEnums.TYPE1.getId()
                || communicateTelephoneRecords.getType() == AppCommunicateTelephoneTypeEnums.TYPE3.getId()
                ? AppSendMsgTypeEnums.TYPE10 : AppSendMsgTypeEnums.TYPE12;

        appChattingRecords.setMsgType((long) msgType.getId());

        JSONObject content = new JSONObject();
        content.put("content", msgType.getDesc());
        // 计算通话时长,时间结果的格式为HH:mm:ss
        long timeDifference = communicateTelephoneRecords.getHangUpTime()
                .getTime() - communicateTelephoneRecords.getConnectTime().getTime();
        long hours = timeDifference / (1000 * 60 * 60);
        long minutes = (timeDifference % (1000 * 60 * 60)) / (1000 * 60);
        long seconds = (timeDifference % (1000 * 60)) / 1000;
        if (seconds == 0) {
            seconds = 1;
        }
        content.put("time", hours + ":" + minutes + ":" + seconds);
        content.put("gold", communicateTelephoneRecords.getConsumptionGold().negate());
        content.put("flag", AppCommunicateTelephoneStatusTypeEnums.TYPE3.getDesc());

        appChattingRecords.setContent(content.toJSONString());

        appChattingRecordsService.insertAppChattingRecords(appChattingRecords);
        appPrivateLetterListService.addChatRelation(communicateTelephoneRecords.getInitiateUserId(), communicateTelephoneRecords.getReceiveUserId(), true);
    }
}
