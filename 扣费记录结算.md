1.打电话的过程中，每分钟的第一秒的扣费逻辑是正确的。现在的问题是通话挂断电话和费用不足自动挂断电话之后又进行了一次结算，"id"= 30784就是重复计算的通话账单记录，和每分钟第一秒的扣费重复的了，需要把挂断电话的扣费逻辑移除，只保留渠道分佣和公会长收益的账单记录；

2.app_communicate_telephone_records记录中的consumption_gold，应该取app_user_points_bill的object_id都为当前通话id的数据，并全部相加total_amount字段的值，才是本次通话的真实消耗的金币数量；

3.每分钟的扣费逻辑，记录的remarks_msg，都采用挂断电话时的`与【开发男】视频通话收入`格式记录数据


{
"app_user_points_bill": [
	{
		"id" : 30786,
		"user_id" : 4402,
		"bill_type" : 7,
		"gift_income_scale" : null,
		"total_amount" : 100.00,
		"amount" : 5.00,
		"object_id" : 14934,
		"remarks_msg" : "与【开发男】视频通话收入",
		"chat_room_id" : null,
		"user_contribution_value" : null,
		"to_user_charm_value" : null,
		"created_by" : null,
		"updated_by" : null,
		"created_time" : "2025-07-19 14:02:42",
		"updated_time" : null,
		"deleted" : 0,
		"guild_id" : null
	},
	{
		"id" : 30787,
		"user_id" : 4402,
		"bill_type" : 7,
		"gift_income_scale" : null,
		"total_amount" : 51.00,
		"amount" : 2.55,
		"object_id" : 14934,
		"remarks_msg" : "与【开发男】视频通话收入",
		"chat_room_id" : null,
		"user_contribution_value" : null,
		"to_user_charm_value" : null,
		"created_by" : null,
		"updated_by" : null,
		"created_time" : "2025-07-19 14:03:42",
		"updated_time" : null,
		"deleted" : 0,
		"guild_id" : null
	},
	{
		"id" : 30788,
		"user_id" : 4397,
		"bill_type" : 24,
		"gift_income_scale" : null,
		"total_amount" : 200.00,
		"amount" : 1.00,
		"object_id" : 14864,
		"remarks_msg" : "渠道分佣得到【我是你好女】的接听电话收益",
		"chat_room_id" : null,
		"user_contribution_value" : null,
		"to_user_charm_value" : null,
		"created_by" : null,
		"updated_by" : null,
		"created_time" : "2025-07-19 14:03:59",
		"updated_time" : null,
		"deleted" : 0,
		"guild_id" : null
	}
]}
